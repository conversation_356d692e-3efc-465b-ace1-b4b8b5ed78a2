import logging
from typing import Dict
from datetime import datetime

from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.filters import Command
from aiogram.types import Message

from app.config import settings, SYSTEM_PROMPTS
from app.core.event_bus import event_bus
from app.utils.events import (
    MessageReceived, 
    ResponseReady, 
    UserSessionStarted, 
    ErrorOccurred
)

logger = logging.getLogger(__name__)


class TelegramBot:
    """Telegram bot handler with event-driven architecture."""
    
    def __init__(self):
        self.bot = Bot(token=settings.telegram.bot_token)
        self.dp = Dispatcher()
        self.user_sessions: Dict[int, datetime] = {}
        
        # Register handlers
        self._register_handlers()

        # Subscribe to events (will be done during start_polling)
    
    def _register_handlers(self):
        """Register message and command handlers."""
        self.dp.message.register(self.handle_start_command, Command("start"))
        self.dp.message.register(self.handle_help_command, Command("help"))
        self.dp.message.register(self.handle_message)
    
    async def handle_start_command(self, message: Message):
        """Handle /start command."""
        try:
            # Validate that we have the required information
            if not message.from_user:
                logger.warning("Cannot handle start command: missing user information")
                return

            user_id = message.from_user.id

            # Track user session
            await self._start_user_session(message)

            # Send welcome message
            await message.reply(
                SYSTEM_PROMPTS["welcome_message"],
                parse_mode="Markdown"
            )

            logger.info(f"User {user_id} started session")

        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await self._handle_error(message, "start_command", str(e))
    
    async def handle_help_command(self, message: Message):
        """Handle /help command."""
        try:
            await message.reply(
                SYSTEM_PROMPTS["help_message"],
                parse_mode="Markdown"
            )
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await self._handle_error(message, "help_command", str(e))
    
    async def handle_message(self, message: Message):
        """Handle regular messages and publish MessageReceived event."""
        try:
            # Skip if it's a command (already handled)
            if message.text and message.text.startswith('/'):
                return

            # Validate that we have the required information
            if not message.from_user or not message.chat:
                logger.warning("Cannot handle message: missing user or chat information")
                return

            user_id = message.from_user.id
            chat_id = message.chat.id

            # Start session if not exists
            if user_id not in self.user_sessions:
                await self._start_user_session(message)

            # Basic message validation
            if not message.text or len(message.text.strip()) == 0:
                await message.reply("Please send a text message.")
                return

            if len(message.text) > 4000:  # Reasonable limit
                await message.reply("Message is too long. Please keep it under 4000 characters.")
                return

            # Publish MessageReceived event
            event = MessageReceived(
                user_id=user_id,
                chat_id=chat_id,
                message_id=message.message_id,
                message_text=message.text,
                username=message.from_user.username,
                first_name=message.from_user.first_name,
                last_name=message.from_user.last_name,
                timestamp=datetime.now()
            )

            await event_bus.publish(event)
            logger.debug(f"Published MessageReceived event for user {user_id}")

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await self._handle_error(message, "message_handler", str(e))
    
    @event_bus.subscribe(ResponseReady)
    async def handle_response_ready(self, event: ResponseReady):
        """Handle ResponseReady events and send messages to users."""
        try:
            parse_mode = None
            if event.response_type == "markdown":
                parse_mode = "Markdown"
            elif event.response_type == "html":
                parse_mode = "HTML"
            
            await self.bot.send_message(
                chat_id=event.chat_id,
                text=event.response_text,
                parse_mode=parse_mode
            )
            
            logger.debug(f"Sent response to chat {event.chat_id}")
            
        except Exception as e:
            logger.error(f"Error sending response: {e}")
            # Try to send a simple error message
            try:
                await self.bot.send_message(
                    chat_id=event.chat_id,
                    text=SYSTEM_PROMPTS["error_messages"]["general_error"]
                )
            except Exception as e:
                logger.error(f"Failed to send fallback error message: {e}")
                pass  # If we can't even send an error message, log and continue
    
    async def _start_user_session(self, message: Message):
        """Start a new user session."""
        # Validate that we have the required information
        if not message.from_user or not message.chat:
            logger.warning("Cannot start user session: missing user or chat information")
            return

        user_id = message.from_user.id
        chat_id = message.chat.id

        self.user_sessions[user_id] = datetime.now()

        # Publish UserSessionStarted event
        event = UserSessionStarted(
            user_id=user_id,
            chat_id=chat_id,
            username=message.from_user.username,
            first_name=message.from_user.first_name,
            last_name=message.from_user.last_name
        )
        
        await event_bus.publish(event)
    
    async def _handle_error(self, message: Message, component: str, error_msg: str):
        """Handle errors and publish ErrorOccurred event."""
        user_id = message.from_user.id if message.from_user else None
        chat_id = message.chat.id if message.chat else None
        message_id = message.message_id if message else None
        
        # Publish error event
        error_event = ErrorOccurred(
            user_id=user_id,
            chat_id=chat_id,
            message_id=message_id,
            error_type="telegram_bot_error",
            error_message=error_msg,
            component=component
        )
        
        await event_bus.publish(error_event)
        
        # Send user-friendly error message
        try:
            if message:
                await message.reply(SYSTEM_PROMPTS["error_messages"]["general_error"])
        except Exception as e:
            logger.error(f"Failed to send error message to user: {e}")
            pass  # If we can't send error message, just log
    
    async def start_polling(self):
        """Start the bot with polling."""
        logger.info("Starting Telegram bot with polling...")

        # Subscribe to events
        await event_bus.subscribe_tagged_methods(self)

        await self.dp.start_polling(self.bot)
    
    async def stop(self):
        """Stop the bot and cleanup."""
        logger.info("Stopping Telegram bot...")
        await self.bot.session.close()
