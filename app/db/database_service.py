"""
Database Service for centralized database operations.

This service provides a high-level interface for database operations,
implementing the IDatabaseService interface and using repositories
for actual data access.
"""

import logging
from typing import List, Optional
from datetime import datetime, timedelta

from app.utils.models import BotUser, ConversationMessage
from .interfaces import IDatabaseService
from .repository.bot_user_repository import BotUserRepository
from .repository.conversation_message_repository import ConversationMessageRepository
from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class DatabaseService(IDatabaseService):
    """Centralized database service for all database operations."""
    
    def __init__(self):
        self.connection_manager = get_connection_manager()
        self.user_repository = BotUserRepository()
        self.message_repository = ConversationMessageRepository()
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the database service."""
        if self._initialized:
            logger.debug("DatabaseService already initialized")
            return
        
        try:
            logger.info("Initializing DatabaseService...")
            
            # Initialize connection
            self.connection_manager.connect()
            
            self._initialized = True
            logger.info("DatabaseService initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize DatabaseService: {e}")
            raise
    
    async def close(self) -> None:
        """Close database connections."""
        try:
            self.connection_manager.disconnect()
            self._initialized = False
            logger.info("DatabaseService closed")
        except Exception as e:
            logger.error(f"Error closing DatabaseService: {e}")
    
    # User operations
    async def get_user_by_telegram_id(self, telegram_user_id: int) -> Optional[BotUser]:
        """Get a user by their Telegram user ID."""
        return await self.user_repository.get_user_by_telegram_id(telegram_user_id)
    
    async def create_user(self, user: BotUser) -> BotUser:
        """Create a new user."""
        return await self.user_repository.create_user(user)
    
    async def update_user(self, user: BotUser) -> BotUser:
        """Update an existing user."""
        return await self.user_repository.update_user(user)
    
    async def delete_user(self, telegram_user_id: int) -> bool:
        """Delete a user by their Telegram user ID."""
        return await self.user_repository.delete_user(telegram_user_id)
    
    async def update_user_last_seen(self, telegram_user_id: int) -> bool:
        """Update a user's last seen timestamp."""
        return await self.user_repository.update_last_seen(telegram_user_id)
    
    async def increment_user_message_count(self, telegram_user_id: int) -> bool:
        """Increment a user's message count."""
        return await self.user_repository.increment_message_count(telegram_user_id)
    
    # Message operations
    async def create_message(self, message: ConversationMessage) -> ConversationMessage:
        """Create a new message."""
        return await self.message_repository.create_message(message)
    
    async def get_message_by_telegram_id(
        self, 
        telegram_message_id: int, 
        chat_id: int
    ) -> Optional[ConversationMessage]:
        """Get a message by its Telegram message ID and chat ID."""
        return await self.message_repository.get_message_by_telegram_id(
            telegram_message_id, chat_id
        )
    
    async def update_message(self, message: ConversationMessage) -> ConversationMessage:
        """Update an existing message."""
        return await self.message_repository.update_message(message)
    
    async def get_conversation_history(
        self,
        chat_id: int,
        telegram_user_id: int,
        limit: int = 10,
        offset: int = 0
    ) -> List[ConversationMessage]:
        """Get conversation history for a user in a chat."""
        return await self.message_repository.get_conversation_history(
            chat_id, telegram_user_id, limit, offset
        )
    
    async def get_recent_messages(
        self,
        chat_id: int,
        hours: int = 24,
        limit: int = 100
    ) -> List[ConversationMessage]:
        """Get recent messages in a chat."""
        return await self.message_repository.get_recent_messages(
            chat_id, hours, limit
        )
    
    async def get_user_recent_messages(
        self,
        telegram_user_id: int,
        limit: int = 50
    ) -> List[ConversationMessage]:
        """Get recent messages from a specific user across all chats."""
        try:
            # For now, return empty list as this requires more complex querying
            # In a real implementation, you might want to add this to the repository
            logger.warning(f"get_user_recent_messages not fully implemented for user {telegram_user_id}")
            return []
            
        except Exception as e:
            logger.error(f"Error getting user recent messages: {e}")
            return []
    
    async def get_unprocessed_messages(self, limit: int = 100) -> List[ConversationMessage]:
        """Get unprocessed messages."""
        return await self.message_repository.get_unprocessed_messages(limit)
    
    async def mark_message_as_processed(
        self,
        telegram_message_id: int,
        chat_id: int,
        response_text: str,
        processing_time: float
    ) -> bool:
        """Mark a message as processed."""
        return await self.message_repository.mark_message_as_processed(
            telegram_message_id, chat_id, response_text, processing_time
        )
    
    async def delete_old_messages(self, days: int = 30) -> int:
        """Delete old messages."""
        return await self.message_repository.delete_old_messages(days)
    
    # Health check and statistics
    async def get_health_status(self) -> dict:
        """Get database health status."""
        try:
            # Check connection health
            connection_health = await self.connection_manager.health_check()
            
            health = {
                "status": "healthy" if connection_health.get("status") == "healthy" else "unhealthy",
                "initialized": self._initialized,
                "connection": connection_health,
                "timestamp": datetime.now().isoformat()
            }
            
            return health
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "initialized": self._initialized,
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_statistics(self) -> dict:
        """Get database statistics."""
        try:
            # Get basic statistics
            stats = {
                "total_users": await self.user_repository.get_user_count(),
                "total_messages": await self.message_repository.get_message_count(),
                "active_users_7d": len(await self.user_repository.get_active_users(7)),
                "messages_24h": len(await self.message_repository.get_recent_messages(
                    chat_id=0, hours=24, limit=1000  # This is a simplified approach
                ))
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database statistics: {e}")
            return {
                "error": str(e)
            }
    
    def is_initialized(self) -> bool:
        """Check if the service is initialized."""
        return self._initialized
    
    def is_connected(self) -> bool:
        """Check if connected to database."""
        return self.connection_manager.is_connected()


# Global database service instance
database_service = DatabaseService()
