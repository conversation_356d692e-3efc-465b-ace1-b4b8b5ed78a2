"""
Message Validator for preprocessing messages.

This module provides message validation functionality including
content sanitization, rate limiting, and basic validation checks.
"""

import logging
import re
import html
from datetime import datetime, timedelta
from typing import Dict, Any, List
from collections import defaultdict

logger = logging.getLogger(__name__)


class MessageValidator:
    """Validates and sanitizes incoming messages."""
    
    def __init__(self, rate_limit_per_minute: int = 10):
        """
        Initialize the message validator.
        
        Args:
            rate_limit_per_minute: Maximum messages per user per minute
        """
        self.rate_limit_per_minute = rate_limit_per_minute
        self.rate_limits: Dict[int, List[datetime]] = defaultdict(list)
        
        # Patterns for content sanitization
        self.malicious_patterns = [
            r'<script[^>]*>.*?</script>',  # Script tags
            r'javascript:',  # JavaScript URLs
            r'data:.*?base64',  # Base64 data URLs
            r'<iframe[^>]*>.*?</iframe>',  # Iframe tags
            r'<object[^>]*>.*?</object>',  # Object tags
            r'<embed[^>]*>.*?</embed>',  # Embed tags
        ]
    
    async def validate_message(self, user_id: int, message_text: str) -> Dict[str, Any]:
        """
        Validate a message for content, length, and rate limiting.
        
        Args:
            user_id: The user ID sending the message
            message_text: The message text to validate
            
        Returns:
            Dictionary containing validation results
        """
        result = {
            "is_valid": True,
            "sanitized_message": message_text,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Check for empty message
            if not message_text or not message_text.strip():
                result["is_valid"] = False
                result["errors"].append("empty_message")
                return result
            
            # Check message length
            if len(message_text) > 4000:  # Telegram's limit is 4096
                result["is_valid"] = False
                result["errors"].append("message_too_long")
                return result
            
            # Check rate limiting
            if not self._check_rate_limit(user_id):
                result["is_valid"] = False
                result["errors"].append("rate_limit_exceeded")
                return result
            
            # Sanitize content
            sanitized_message, was_sanitized = self._sanitize_content(message_text)
            result["sanitized_message"] = sanitized_message
            
            if was_sanitized:
                result["warnings"].append("content_sanitized")
            
            # Check for spam patterns
            if self._is_spam_like(message_text):
                result["warnings"].append("potential_spam")
            
            # Update rate limit tracking
            self._update_rate_limit(user_id)
            
        except Exception as e:
            logger.error(f"Error validating message: {e}")
            result["is_valid"] = False
            result["errors"].append("validation_error")
        
        return result
    
    def _check_rate_limit(self, user_id: int) -> bool:
        """Check if user is within rate limit."""
        now = datetime.now()
        cutoff = now - timedelta(minutes=1)
        
        # Clean old entries
        self.rate_limits[user_id] = [
            timestamp for timestamp in self.rate_limits[user_id]
            if timestamp > cutoff
        ]
        
        # Check if under limit
        return len(self.rate_limits[user_id]) < self.rate_limit_per_minute
    
    def _update_rate_limit(self, user_id: int) -> None:
        """Update rate limit tracking for user."""
        self.rate_limits[user_id].append(datetime.now())
    
    def _sanitize_content(self, message_text: str) -> tuple[str, bool]:
        """
        Sanitize message content by removing potentially malicious patterns.
        
        Returns:
            Tuple of (sanitized_message, was_sanitized)
        """
        original_message = message_text
        sanitized = message_text
        
        # Remove malicious patterns
        for pattern in self.malicious_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        
        # HTML escape any remaining HTML-like content
        sanitized = html.escape(sanitized)
        
        # Remove excessive whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        
        was_sanitized = sanitized != original_message
        return sanitized, was_sanitized
    
    def _is_spam_like(self, message_text: str) -> bool:
        """Check if message appears to be spam-like."""
        # Check for excessive repetition
        words = message_text.split()
        if len(words) > 5:
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                return True
        
        # Check for excessive capitalization
        if len(message_text) > 10:
            caps_ratio = sum(1 for c in message_text if c.isupper()) / len(message_text)
            if caps_ratio > 0.7:  # More than 70% caps
                return True
        
        return False
