import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional, AsyncGenerator
from abc import ABC, abstractmethod

from app.config import settings
from app.core.event_bus import event_bus
from app.utils.events import MessagePreprocessed, LLMResponseReceived, ErrorOccurred

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response from the LLM."""
        pass
    
    @abstractmethod
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream response from the LLM."""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name."""
        pass


class MockLLMProvider(LLMProvider):
    """Mock LLM provider for testing and development."""
    
    @property
    def name(self) -> str:
        return "mock"
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate a mock response."""
        await asyncio.sleep(0.5)  # Simulate processing time
        
        # Simple mock response based on prompt content
        response_text = self._generate_mock_response(prompt)
        
        return {
            "response": response_text,
            "tool_calls": [],
            "tokens_used": len(prompt.split()) + len(response_text.split()),
            "processing_time": 0.5
        }
    
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream a mock response."""
        response = await self.generate_response(prompt, **kwargs)
        words = response["response"].split()
        
        for word in words:
            yield word + " "
            await asyncio.sleep(0.1)  # Simulate streaming delay
    
    def _generate_mock_response(self, prompt: str) -> str:
        """Generate a contextual mock response."""
        prompt_lower = prompt.lower()
        
        if "schedule" in prompt_lower or "meeting" in prompt_lower:
            return "I'd be happy to help you schedule a meeting! To create a calendar event, I'll need some details like the date, time, and duration. Could you please provide more specific information about when you'd like to schedule this meeting?"
        
        elif "calendar" in prompt_lower or "events" in prompt_lower:
            return "I can help you manage your calendar events. You can ask me to create new events, check your schedule, find available time slots, or update existing events. What would you like to do with your calendar?"
        
        elif "available" in prompt_lower or "free time" in prompt_lower:
            return "Let me check your availability. Based on your current schedule, I can suggest some free time slots. Would you like me to look for specific days or times that work best for you?"
        
        elif "help" in prompt_lower:
            return "I'm Zeitwahl, your calendar assistant! I can help you:\n• Schedule meetings and events\n• Check your calendar\n• Find available time slots\n• Manage recurring events\n• Set reminders\n\nWhat would you like me to help you with?"
        
        else:
            return "I understand you'd like help with your calendar. Could you please be more specific about what you'd like me to do? For example, you can ask me to schedule a meeting, check your calendar, or find available time slots."


class GeminiProvider(LLMProvider):
    """Google Gemini LLM provider."""
    
    def __init__(self, api_key: str, model: str = "gemini-pro"):
        self.api_key = api_key
        self.model = model
    
    @property
    def name(self) -> str:
        return "gemini"
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using Gemini API."""
        # In a real implementation, this would call the actual Gemini API
        # For now, return mock data
        logger.warning("Gemini API not implemented, using mock response")
        mock_provider = MockLLMProvider()
        return await mock_provider.generate_response(prompt, **kwargs)
    
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using Gemini API."""
        # In a real implementation, this would stream from Gemini API
        logger.warning("Gemini streaming not implemented, using mock response")
        mock_provider = MockLLMProvider()
        async for chunk in mock_provider.stream_response(prompt, **kwargs):
            yield chunk


class DeepseekProvider(LLMProvider):
    """Deepseek LLM provider."""
    
    def __init__(self, api_key: str, model: str = "deepseek-chat", base_url: str = "https://api.deepseek.com"):
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
    
    @property
    def name(self) -> str:
        return "deepseek"
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using Deepseek API."""
        # In a real implementation, this would call the actual Deepseek API
        # For now, return mock data
        logger.warning("Deepseek API not implemented, using mock response")
        mock_provider = MockLLMProvider()
        return await mock_provider.generate_response(prompt, **kwargs)
    
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using Deepseek API."""
        # In a real implementation, this would stream from Deepseek API
        logger.warning("Deepseek streaming not implemented, using mock response")
        mock_provider = MockLLMProvider()
        async for chunk in mock_provider.stream_response(prompt, **kwargs):
            yield chunk


class LLMService:
    """LLM service with multi-provider support and automatic failover."""
    
    def __init__(self):
        self.providers: Dict[str, LLMProvider] = {}
        self.primary_provider = settings.llm.primary_provider
        self.fallback_providers = settings.llm.fallback_providers
        
        self._initialize_providers()
        
        # Note: Event subscription will be handled by the actor that uses this service
    
    def _initialize_providers(self):
        """Initialize available LLM providers."""
        # Mock provider (always available for testing)
        self.providers["mock"] = MockLLMProvider()
        
        # Gemini provider
        if settings.llm.gemini_api_key:
            self.providers["gemini"] = GeminiProvider(
                settings.llm.gemini_api_key,
                settings.llm.gemini_model
            )
        
        # Deepseek provider
        if settings.llm.deepseek_api_key:
            self.providers["deepseek"] = DeepseekProvider(
                settings.llm.deepseek_api_key,
                settings.llm.deepseek_model,
                settings.llm.deepseek_base_url
            )
        
        logger.info(f"Initialized LLM providers: {list(self.providers.keys())}")
    
    @event_bus.subscribe(MessagePreprocessed)
    async def handle_message_preprocessed(self, event: MessagePreprocessed):
        """Handle MessagePreprocessed events and generate LLM responses."""
        try:
            logger.info(f"Generating LLM response for user {event.user_id}")
            
            start_time = time.time()
            
            # Try primary provider first, then fallbacks
            providers_to_try = [self.primary_provider] + self.fallback_providers
            
            response = None
            provider_used = None
            
            for provider_name in providers_to_try:
                if provider_name in self.providers:
                    try:
                        provider = self.providers[provider_name]
                        response = await provider.generate_response(
                            event.processed_prompt,
                            max_tokens=settings.llm.max_tokens,
                            temperature=settings.llm.temperature
                        )
                        provider_used = provider_name
                        logger.info(f"Successfully used provider: {provider_name}")
                        break
                    except Exception as e:
                        logger.warning(f"Provider {provider_name} failed: {e}")
                        continue
            
            if not response:
                raise Exception("All LLM providers failed")
            
            processing_time = time.time() - start_time
            
            # Parse tool calls from response (if any)
            tool_calls = self._extract_tool_calls(response.get("response", ""))
            
            # Publish LLMResponseReceived event
            llm_event = LLMResponseReceived(
                user_id=event.user_id,
                chat_id=event.chat_id,
                message_id=event.message_id,
                response_text=response.get("response", ""),
                tool_calls=tool_calls,
                provider_used=provider_used,
                tokens_used=response.get("tokens_used", 0),
                processing_time=processing_time
            )
            
            await event_bus.publish(llm_event)
            logger.info(f"Published LLMResponseReceived event for user {event.user_id}")
            
        except Exception as e:
            logger.error(f"Error in LLM processing: {e}")
            await self._handle_llm_error(event, str(e))
    
    def _extract_tool_calls(self, response_text: str) -> List[Dict[str, Any]]:
        """Extract tool calls from LLM response."""
        tool_calls = []
        
        # Simple pattern matching for tool calls
        # In a real implementation, this would be more sophisticated
        if "create_calendar_event" in response_text.lower():
            # Mock tool call extraction
            tool_calls.append({
                "name": "create_calendar_event",
                "parameters": {
                    "title": "Meeting",
                    "start_time": "2024-01-01T10:00:00Z",
                    "end_time": "2024-01-01T11:00:00Z"
                }
            })
        
        return tool_calls
    
    async def _handle_llm_error(self, event: MessagePreprocessed, error_msg: str):
        """Handle LLM processing errors."""
        error_event = ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            message_id=event.message_id,
            error_type="llm_error",
            error_message="I'm having trouble processing your request. Please try again.",
            component="llm_service"
        )
        
        await event_bus.publish(error_event)
