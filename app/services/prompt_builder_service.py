"""
Prompt Builder Service for constructing comprehensive prompts for LLM processing.

This service takes user context, conversation history, and current message
to build optimized prompts for the LLM that include all necessary context.
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from app.config.prompts import SYSTEM_PROMPTS

logger = logging.getLogger(__name__)


class PromptBuilderService:
    """Service for building comprehensive prompts for LLM processing."""
    
    def __init__(self):
        self.system_prompts = SYSTEM_PROMPTS
    
    async def build_prompt(
        self,
        message_text: str,
        context: Dict[str, Any],
        conversation_history: List[Dict[str, Any]],
        user_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Build a comprehensive prompt for LLM processing.
        
        Args:
            message_text: The current user message
            context: User context information
            conversation_history: Recent conversation history
            user_data: User profile data
            
        Returns:
            Formatted prompt string ready for LLM processing
        """
        try:
            # Build the prompt components
            system_context = self._build_system_context()
            user_context = self._build_user_context(user_data)
            conversation_context = self._build_conversation_context(conversation_history)
            current_message_context = self._build_current_message_context(message_text, context)
            
            # Combine all components
            prompt = f"""{system_context}

{user_context}

{conversation_context}

{current_message_context}

Please provide a helpful, accurate, and contextually appropriate response."""
            
            logger.debug(f"Built prompt with {len(prompt)} characters")
            return prompt
            
        except Exception as e:
            logger.error(f"Error building prompt: {e}")
            # Return a basic prompt as fallback
            return f"""You are Zeitwahl, an AI assistant that helps with scheduling and time management.

User message: {message_text}

Please provide a helpful response."""
    
    def _build_system_context(self) -> str:
        """Build the system context part of the prompt."""
        return """You are Zeitwahl, an intelligent AI assistant specialized in scheduling, time management, and calendar operations.

Your capabilities include:
- Understanding natural language requests about time and scheduling
- Helping users manage their calendars and appointments
- Providing time zone conversions and scheduling assistance
- Offering productivity and time management advice
- Answering questions about dates, times, and scheduling conflicts

Guidelines:
- Be helpful, accurate, and concise
- Always consider the user's timezone when dealing with time-related requests
- If you need clarification, ask specific questions
- Provide actionable suggestions when possible
- Be respectful of the user's time and preferences"""
    
    def _build_user_context(self, user_data: Optional[Dict[str, Any]]) -> str:
        """Build the user context part of the prompt."""
        if not user_data:
            return "User Context: New user (no profile information available)"
        
        context_parts = ["User Context:"]
        
        if user_data.get("first_name"):
            context_parts.append(f"- Name: {user_data['first_name']}")
        
        if user_data.get("timezone"):
            context_parts.append(f"- Timezone: {user_data['timezone']}")
        
        if user_data.get("total_messages"):
            context_parts.append(f"- Previous interactions: {user_data['total_messages']} messages")
        
        if user_data.get("last_seen_at"):
            context_parts.append(f"- Last seen: {user_data['last_seen_at']}")
        
        return "\n".join(context_parts)
    
    def _build_conversation_context(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Build the conversation context part of the prompt."""
        if not conversation_history:
            return "Conversation Context: This is the start of a new conversation."
        
        context_parts = ["Recent Conversation History:"]
        
        # Limit to last 5 messages to keep prompt manageable
        recent_messages = conversation_history[-5:]
        
        for i, message in enumerate(recent_messages):
            timestamp = message.get("timestamp", "")
            if timestamp:
                timestamp_str = f" ({timestamp.strftime('%H:%M')})" if hasattr(timestamp, 'strftime') else f" ({timestamp})"
            else:
                timestamp_str = ""
            
            sender = "Bot" if message.get("is_from_bot") else "User"
            text = message.get("message_text", "")
            
            # Truncate very long messages
            if len(text) > 100:
                text = text[:97] + "..."
            
            context_parts.append(f"{i+1}. {sender}{timestamp_str}: {text}")
        
        return "\n".join(context_parts)
    
    def _build_current_message_context(self, message_text: str, context: Dict[str, Any]) -> str:
        """Build the current message context part of the prompt."""
        context_parts = ["Current Request:"]
        context_parts.append(f"User message: {message_text}")
        
        # Add timestamp context
        current_time = datetime.now()
        context_parts.append(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Add any relevant patterns or context
        patterns = context.get("message_patterns", {})
        if patterns.get("most_active_hour"):
            context_parts.append(f"User typically active around: {patterns['most_active_hour']}:00")
        
        return "\n".join(context_parts)
    
    async def build_registration_prompt(self, user_name: Optional[str] = None) -> str:
        """Build a prompt for new user registration."""
        greeting = f"Hello {user_name}!" if user_name else "Hello!"
        
        return f"""{greeting} Welcome to Zeitwahl, your AI scheduling assistant.

I notice you're new here. To provide you with the best assistance, I'd like to know your timezone.

Please tell me:
1. What timezone are you in? (e.g., "UTC", "America/New_York", "Europe/London")
2. How would you like me to help you with scheduling and time management?

You can also type /help to see what I can do for you."""
    
    async def build_irrelevant_message_prompt(self) -> str:
        """Build a prompt for handling irrelevant messages."""
        return """I'm Zeitwahl, your AI scheduling and time management assistant. 

I specialize in helping with:
- Calendar management and scheduling
- Time zone conversions
- Meeting planning and coordination
- Productivity and time management advice
- Date and time calculations

Your message doesn't seem to be related to scheduling or time management. How can I help you with your calendar or time-related needs today?

Type /help for more information about what I can do."""
    
    def estimate_token_count(self, prompt: str) -> int:
        """
        Estimate the token count for a prompt.
        
        This is a rough estimation based on the rule that 1 token ≈ 4 characters.
        For more accurate counting, integrate with the actual tokenizer.
        """
        return len(prompt) // 4
