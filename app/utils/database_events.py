"""
Database operation events for event bus communication.

This module defines events for database CRUD operations that maintain
state consistency while using async event bus communication.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional
from bson import ObjectId

from app.core.event_bus import BaseEvent
from app.utils.models import BotUser, ConversationMessage


# Base classes for database operations
@dataclass
class DatabaseRequest(BaseEvent):
    """Base class for database request events."""
    request_id: str = ""  # Unique identifier to correlate request/response
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if not self.request_id:
            import uuid
            self.request_id = str(uuid.uuid4())
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class DatabaseResponse(BaseEvent):
    """Base class for database response events."""
    request_id: str = ""  # Correlates with the original request
    success: bool = True
    error_message: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


# BotUser CRUD Events
@dataclass
class CreateUserRequest(DatabaseRequest):
    """Request to create a new bot user."""
    user: Optional[BotUser] = None


@dataclass
class CreateUserResponse(DatabaseResponse):
    """Response from creating a bot user."""
    user: Optional[BotUser] = None


@dataclass
class GetUserByTelegramIdRequest(DatabaseRequest):
    """Request to get a user by Telegram ID."""
    telegram_user_id: int = 0


@dataclass
class GetUserByTelegramIdResponse(DatabaseResponse):
    """Response from getting a user by Telegram ID."""
    user: Optional[BotUser] = None


@dataclass
class GetUserByIdRequest(DatabaseRequest):
    """Request to get a user by MongoDB ObjectId."""
    user_id: Optional[ObjectId] = None


@dataclass
class GetUserByIdResponse(DatabaseResponse):
    """Response from getting a user by MongoDB ObjectId."""
    user: Optional[BotUser] = None


@dataclass
class UpdateUserRequest(DatabaseRequest):
    """Request to update an existing user."""
    user: Optional[BotUser] = None


@dataclass
class UpdateUserResponse(DatabaseResponse):
    """Response from updating a user."""
    user: Optional[BotUser] = None


@dataclass
class UpdateUserLastSeenRequest(DatabaseRequest):
    """Request to update user's last seen timestamp."""
    telegram_user_id: int = 0


@dataclass
class UpdateUserLastSeenResponse(DatabaseResponse):
    """Response from updating user's last seen."""
    updated: bool = False


@dataclass
class IncrementUserMessageCountRequest(DatabaseRequest):
    """Request to increment user's message count."""
    telegram_user_id: int = 0


@dataclass
class IncrementUserMessageCountResponse(DatabaseResponse):
    """Response from incrementing user's message count."""
    updated: bool = False


# ConversationMessage CRUD Events
@dataclass
class CreateMessageRequest(DatabaseRequest):
    """Request to create a new conversation message."""
    message: Optional[ConversationMessage] = None


@dataclass
class CreateMessageResponse(DatabaseResponse):
    """Response from creating a conversation message."""
    message: Optional[ConversationMessage] = None


@dataclass
class GetMessageByTelegramIdRequest(DatabaseRequest):
    """Request to get a message by Telegram message ID and chat ID."""
    telegram_message_id: int = 0
    chat_id: int = 0


@dataclass
class GetMessageByTelegramIdResponse(DatabaseResponse):
    """Response from getting a message by Telegram ID."""
    message: Optional[ConversationMessage] = None


@dataclass
class GetMessageByIdRequest(DatabaseRequest):
    """Request to get a message by MongoDB ObjectId."""
    message_id: Optional[ObjectId] = None


@dataclass
class GetMessageByIdResponse(DatabaseResponse):
    """Response from getting a message by MongoDB ObjectId."""
    message: Optional[ConversationMessage] = None


@dataclass
class UpdateMessageRequest(DatabaseRequest):
    """Request to update an existing message."""
    message: Optional[ConversationMessage] = None


@dataclass
class UpdateMessageResponse(DatabaseResponse):
    """Response from updating a message."""
    message: Optional[ConversationMessage] = None


@dataclass
class GetConversationHistoryRequest(DatabaseRequest):
    """Request to get conversation history."""
    chat_id: int = 0
    telegram_user_id: int = 0
    limit: int = 50
    offset: int = 0


@dataclass
class GetConversationHistoryResponse(DatabaseResponse):
    """Response from getting conversation history."""
    messages: Optional[List[ConversationMessage]] = None

    def __post_init__(self):
        super().__post_init__()
        if self.messages is None:
            self.messages = []


@dataclass
class GetRecentMessagesRequest(DatabaseRequest):
    """Request to get recent messages from a chat."""
    chat_id: int = 0
    hours: int = 24
    limit: int = 100


@dataclass
class GetRecentMessagesResponse(DatabaseResponse):
    """Response from getting recent messages."""
    messages: Optional[List[ConversationMessage]] = None

    def __post_init__(self):
        super().__post_init__()
        if self.messages is None:
            self.messages = []


@dataclass
class GetUnprocessedMessagesRequest(DatabaseRequest):
    """Request to get unprocessed messages."""
    limit: int = 50


@dataclass
class GetUnprocessedMessagesResponse(DatabaseResponse):
    """Response from getting unprocessed messages."""
    messages: Optional[List[ConversationMessage]] = None

    def __post_init__(self):
        super().__post_init__()
        if self.messages is None:
            self.messages = []


@dataclass
class MarkMessageAsProcessedRequest(DatabaseRequest):
    """Request to mark a message as processed."""
    message_id: Optional[ObjectId] = None


@dataclass
class MarkMessageAsProcessedResponse(DatabaseResponse):
    """Response from marking a message as processed."""
    updated: bool = False


# Database initialization events
@dataclass
class InitializeDatabaseRequest(DatabaseRequest):
    """Request to initialize the database."""
    pass


@dataclass
class InitializeDatabaseResponse(DatabaseResponse):
    """Response from database initialization."""
    initialization_results: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        super().__post_init__()
        if self.initialization_results is None:
            self.initialization_results = {}


@dataclass
class DatabaseHealthCheckRequest(DatabaseRequest):
    """Request for database health check."""
    pass


@dataclass
class DatabaseHealthCheckResponse(DatabaseResponse):
    """Response from database health check."""
    health_status: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        super().__post_init__()
        if self.health_status is None:
            self.health_status = {}


# State management events for maintaining consistency
@dataclass
class DatabaseOperationStarted(BaseEvent):
    """Event published when a database operation starts."""
    request_id: str = ""
    operation_type: str = ""
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class DatabaseOperationCompleted(BaseEvent):
    """Event published when a database operation completes."""
    request_id: str = ""
    operation_type: str = ""
    success: bool = True
    duration: float = 0.0
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class DatabaseOperationFailed(BaseEvent):
    """Event published when a database operation fails."""
    request_id: str = ""
    operation_type: str = ""
    error_message: str = ""
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
